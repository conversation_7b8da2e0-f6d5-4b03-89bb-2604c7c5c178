import { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } from 'electron'
import fs from 'fs';
import http from 'node:http';

import WebTorrent, { type Torrent, type TorrentFile } from 'webtorrent'

import { fileURLToPath } from 'node:url'
import path from 'node:path'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
    },
    title: "Project Zine",
    width: 1280,
    height: 720
  })

  // Remove top menu bar
  Menu.setApplicationMenu(null);

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

const client = new WebTorrent({ utp: false });
let torrentServer: http.Server | null = null;
let torrentServerPort: number | null = null;

app.whenReady().then(() => {
  createWindow();

  torrentServer = client.createServer() as unknown as http.Server;
  if (torrentServer) {
    torrentServer.listen(0, () => {
      const addr = torrentServer!.address();
      if (addr && typeof addr === 'object') {
        torrentServerPort = addr.port;
        console.log(`Torrent streaming server is listening on port: ${torrentServerPort}`);
      }
    });
  }
});

app.on('will-quit', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
  if (torrentServer) {
    torrentServer.close();
  }
  client.destroy();
});

const mime: { [key: string]: string } = {
  '.mp4': 'video/mp4',
  '.mkv': 'video/x-matroska',
  '.webm': 'video/webm',
  '.avi': 'video/x-msvideo',
  '.mov': 'video/quicktime',
  '.flv': 'video/x-flv',
  '.ogg': 'video/ogg',
};

const handleTorrent = (torrent: Torrent, resolve: (value: { url: string; type: string; }) => void, reject: (reason?: any) => void) => {
  console.log('Handling torrent:', torrent.infoHash);

  // Select video files only and pick the first one in natural order
  const videoFiles = torrent.files
    .filter((f: TorrentFile) => {
      const ext = path.extname(f.name).toLowerCase();
      return Object.keys(mime).includes(ext);
    })
    .sort((a: TorrentFile, b: TorrentFile) => a.name.localeCompare(b.name));

  const file = videoFiles.length > 0 ? videoFiles[0] : torrent.files[0];
  const fileExtension = path.extname(file.name).toLowerCase();
  const type = mime[fileExtension] || 'video/mp4'; // Default to mp4 if type is unknown

  let attempts = 0;
  const maxAttempts = 50; // 5 seconds

  const giveUrl = () => {
    if (torrentServerPort) {
      const url = `http://localhost:${torrentServerPort}${file.streamURL}`;
      console.log('Streaming URL for file:', url);
      resolve({ url, type });
    } else {
      attempts++;
      if (attempts > maxAttempts) {
        reject(new Error('Torrent server did not start in time.'));
      } else {
        setTimeout(giveUrl, 100);
      }
    }
  };

  giveUrl();
};

ipcMain.handle('add-torrent', async (_event, torrentId: string | Buffer) => {
  console.log(`Received torrent identifier`);

  return new Promise((resolve, reject) => {
    client.add(torrentId, (torrent) => {
      torrent.on('ready', () => {
        handleTorrent(torrent, resolve, reject);
      });

      torrent.on('download', () => {
        win?.webContents.send('download-progress', {
          progress: torrent.progress,
          downloadSpeed: torrent.downloadSpeed,
          peers: torrent.numPeers,
        });
      });

      torrent.on('done', () => {
        console.log('Torrent download finished');
        win?.webContents.send('download-progress', {
          progress: 1,
          downloadSpeed: 0,
          peers: torrent.numPeers,
        });
      });

      torrent.on('error', (err) => {
        console.error('Torrent error:', err);
        reject(err);
      });
    });
  });
});

ipcMain.handle('open-file-dialog', async () => {
  const { filePaths } = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [{ name: 'Torrents', extensions: ['torrent'] }],
  });

  if (filePaths && filePaths.length > 0) {
    return fs.readFileSync(filePaths[0]);
  }
  return null;
});
