import React, { useState } from 'react';

interface AddTorrentDialogProps {
  onTorrentAdded: (streamingUrl: string, type: string) => void;
}

const AddTorrentDialog: React.FC<AddTorrentDialogProps> = ({ onTorrentAdded }) => {
  const [torrentId, setTorrentId] = useState('');

    const handleAddTorrent = async () => {
    if (torrentId) {
      try {
        const result = await window.electronAPI.addTorrent(torrentId);
        onTorrentAdded(result.url, result.type);
      } catch (error) {
        console.error('Failed to add torrent:', error);
      }
    }
  };

  const handleOpenFile = async () => {
    try {
      const fileBuffer = await window.electronAPI.openFileDialog();
      if (fileBuffer) {
        const result = await window.electronAPI.addTorrent(fileBuffer);
        onTorrentAdded(result.url, result.type);
      }
    } catch (error) {
      console.error('Failed to open file:', error);
    }
  };

  return (
    <div className="p-4 bg-gray-800 rounded-lg">
      <h3 className="text-lg font-bold mb-4">Add New Torrent</h3>
      <input
        type="text"
        value={torrentId}
        onChange={(e) => setTorrentId(e.target.value)}
        placeholder="Enter magnet link or .torrent file path"
        className="w-full p-2 bg-gray-700 rounded mb-4 text-white"
      />
      <button
        onClick={handleAddTorrent}
        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        Add Magnet
      </button>
      <button
        onClick={handleOpenFile}
        className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded ml-2"
      >
        Open .torrent File
      </button>
    </div>
  );
};

export default AddTorrentDialog;
