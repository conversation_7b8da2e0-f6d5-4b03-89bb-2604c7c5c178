{"name": "zine_electron", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@tailwindcss/vite": "^4.1.7", "@types/webtorrent": "^0.110.0", "react": "^18.2.0", "react-dom": "^18.2.0", "webtorrent": "^2.6.8"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js"}