import React, { useEffect, useRef, useState } from 'react';

interface VideoPlayerProps {
  src: string;
  type: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ src, type }) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (videoRef.current) {
      // Reset video when src changes
      videoRef.current.pause();
      videoRef.current.load();
      const playPromise = videoRef.current.play();
      if (playPromise) {
        playPromise.catch(() => {
          // Autoplay with sound might be blocked; try muted first
          if (videoRef.current) {
            videoRef.current.muted = true;
            videoRef.current.play().catch((err) => console.error('Video playback failed', err));
          }
        });
      }
    }
  }, [src]);

  const handleCanPlay = () => setIsLoading(false);

  return (
    <div className="w-full bg-black min-h-[240px] flex items-center justify-center">
      {isLoading && <span className="text-gray-400">Loading video…</span>}
      <video
        ref={videoRef}
        controls
        autoPlay
        onCanPlay={handleCanPlay}
        className="w-full h-auto"
      >
        <source src={src} type={type} />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default VideoPlayer;
