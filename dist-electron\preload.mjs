"use strict";
const electron = require("electron");
const electronAPI = {
  addTorrent: (torrentId) => electron.ipcRenderer.invoke("add-torrent", torrentId),
  openFileDialog: () => electron.ipcRenderer.invoke("open-file-dialog"),
  onDownloadProgress: (callback) => {
    electron.ipcRenderer.on("download-progress", callback);
    return () => {
      electron.ipcRenderer.removeListener("download-progress", callback);
    };
  }
};
electron.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
