import React, { useState, useEffect } from 'react';
import Header from './components/layout/Header';
import Sidebar from './components/layout/Sidebar';
import MainContent from './components/layout/MainContent';
import Footer from './components/layout/Footer';
import TorrentListView from './components/features/TorrentListView';
import AddTorrentDialog from './components/common/AddTorrentDialog';
import VideoPlayer from './components/common/VideoPlayer';

interface DownloadProgress {
  progress: number;
  downloadSpeed: number;
  peers: number;
}

function App() {
  const [video, setVideo] = useState<{ url: string; type: string } | null>(null);
  const [progress, setProgress] = useState<DownloadProgress | null>(null);

  useEffect(() => {
    const removeListener = window.electronAPI.onDownloadProgress((event, progressData) => {
      setProgress(progressData);
    });

    // Cleanup the listener when the component unmounts
    return () => {
      removeListener();
    };
  }, []);

  const handleTorrentAdded = (url: string, type: string) => {
    setVideo({ url, type });
  };

  return (
    <div className="flex flex-col h-screen bg-gray-800 text-white">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <MainContent>
          <AddTorrentDialog onTorrentAdded={handleTorrentAdded} />
          
          {video && (
            <div className="mt-4">
              <VideoPlayer src={video.url} type={video.type} />
            </div>
          )}

          <div className="mt-4">
            <TorrentListView />
          </div>
        </MainContent>
      </div>
      <Footer progress={progress} />
    </div>
  );
}

export default App;
