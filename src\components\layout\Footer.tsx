import React from 'react';

interface DownloadProgress {
  progress: number;
  downloadSpeed: number;
  peers: number;
}

interface FooterProps {
  progress: DownloadProgress | null;
}

// Helper to format bytes into KB/s, MB/s, etc.
const formatSpeed = (bytes: number) => {
  if (bytes === 0) return '0 KB/s';
  const k = 1024;
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

const Footer: React.FC<FooterProps> = ({ progress }) => {
  return (
    <footer className="bg-gray-900 text-white p-2 text-sm text-center">
      {progress ? (
        <p>
          Status: Downloading | Progress: {(progress.progress * 100).toFixed(2)}% | Peers: {progress.peers} | Down: {formatSpeed(progress.downloadSpeed)}
        </p>
      ) : (
        <p>Status: Idle</p>
      )}
    </footer>
  );
};

export default Footer;
