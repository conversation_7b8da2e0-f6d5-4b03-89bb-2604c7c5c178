import { app, BrowserWindow, ipc<PERSON>ain, dialog, Menu } from "electron";
import fs from "fs";
import WebTorrent from "webtorrent";
import { fileURLToPath } from "node:url";
import path from "node:path";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs")
    },
    title: "Project Zine",
    width: 1280,
    height: 720
  });
  Menu.setApplicationMenu(null);
  win.webContents.on("did-finish-load", () => {
    win?.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
const client = new WebTorrent({ utp: false });
let torrentServer = null;
let torrentServerPort = null;
app.whenReady().then(() => {
  createWindow();
  torrentServer = client.createServer();
  if (torrentServer) {
    torrentServer.listen(0, () => {
      const addr = torrentServer.address();
      if (addr && typeof addr === "object") {
        torrentServerPort = addr.port;
        console.log(`Torrent streaming server is listening on port: ${torrentServerPort}`);
      }
    });
  }
});
app.on("will-quit", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
  if (torrentServer) {
    torrentServer.close();
  }
  client.destroy();
});
const mime = {
  ".mp4": "video/mp4",
  ".mkv": "video/x-matroska",
  ".webm": "video/webm",
  ".avi": "video/x-msvideo",
  ".mov": "video/quicktime",
  ".flv": "video/x-flv",
  ".ogg": "video/ogg"
};
const handleTorrent = (torrent, resolve, reject) => {
  console.log("Handling torrent:", torrent.infoHash);
  const videoFiles = torrent.files.filter((f) => {
    const ext = path.extname(f.name).toLowerCase();
    return Object.keys(mime).includes(ext);
  }).sort((a, b) => a.name.localeCompare(b.name));
  const file = videoFiles.length > 0 ? videoFiles[0] : torrent.files[0];
  const fileExtension = path.extname(file.name).toLowerCase();
  const type = mime[fileExtension] || "video/mp4";
  let attempts = 0;
  const maxAttempts = 50;
  const giveUrl = () => {
    if (torrentServerPort) {
      const url = `http://localhost:${torrentServerPort}${file.streamURL}`;
      console.log("Streaming URL for file:", url);
      resolve({ url, type });
    } else {
      attempts++;
      if (attempts > maxAttempts) {
        reject(new Error("Torrent server did not start in time."));
      } else {
        setTimeout(giveUrl, 100);
      }
    }
  };
  giveUrl();
};
ipcMain.handle("add-torrent", async (_event, torrentId) => {
  console.log(`Received torrent identifier`);
  return new Promise((resolve, reject) => {
    client.add(torrentId, (torrent) => {
      torrent.on("ready", () => {
        handleTorrent(torrent, resolve, reject);
      });
      torrent.on("download", () => {
        win?.webContents.send("download-progress", {
          progress: torrent.progress,
          downloadSpeed: torrent.downloadSpeed,
          peers: torrent.numPeers
        });
      });
      torrent.on("done", () => {
        console.log("Torrent download finished");
        win?.webContents.send("download-progress", {
          progress: 1,
          downloadSpeed: 0,
          peers: torrent.numPeers
        });
      });
      torrent.on("error", (err) => {
        console.error("Torrent error:", err);
        reject(err);
      });
    });
  });
});
ipcMain.handle("open-file-dialog", async () => {
  const { filePaths } = await dialog.showOpenDialog({
    properties: ["openFile"],
    filters: [{ name: "Torrents", extensions: ["torrent"] }]
  });
  if (filePaths && filePaths.length > 0) {
    return fs.readFileSync(filePaths[0]);
  }
  return null;
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
