**Project Zine: Desktop MVP Development Plan**

**Overall Goal:** Create a functional desktop torrent streaming and downloading application (<PERSON>) using Electron, React, Vite, TypeScript, Zustand, Tailwind CSS, and Headless UI.

---

**Phase 1: Foundation & Core Tooling Setup (Partially Done, Needs Finalizing)**

*   **Objective:** Ensure the basic development environment is stable, all core tools are integrated, and the team can build and run a basic "Hello World" Electron app with React.
*   **Current Status:** You have the file structure and basic Vite + Electron setup.
*   **Tasks:**
    1.  **Confirm Basic Build & Run:**
        *   Ensure `npm run dev` (or equivalent script) successfully launches the Electron app with the Vite dev server for the React frontend.
        *   Ensure `npm run build` (or equivalent) correctly builds the React app and compiles the Electron TypeScript files into `dist-electron`.
    2.  **Integrate Tailwind CSS:**
        *   Install Tailwind CSS, PostCSS, Autoprefixer.
        *   Configure `tailwind.config.js` and `postcss.config.js`.
        *   Import Tailwind directives into `src/main.css` (or `index.css`).
        *   Verify Tailwind classes work in `App.tsx`.
    3.  **Integrate Headless UI:**
        *   Install `@headlessui/react`.
        *   Import and use a simple Headless UI component (e.g., a `Menu` for a dropdown or a `Switch`) in `App.tsx` and style it with Tailwind to verify integration.
    4.  **Setup Zustand (State Management):**
        *   Install `zustand`.
        *   Create a simple placeholder store (e.g., `src/store/appStore.ts`) with a basic state (like a counter or a theme toggle) and use it in `App.tsx`.
    5.  **ESLint & Prettier:**
        *   You have `.eslintrc.cjs`. Ensure it's configured for TypeScript and React.
        *   Consider adding Prettier for consistent code formatting and integrate it with ESLint (`eslint-config-prettier`, `eslint-plugin-prettier`).
        *   Set up format-on-save in VSCode for the team.
    6.  **Refine `tsconfig.json` & `tsconfig.node.json`:**
        *   Ensure paths, module resolution, and target versions are optimal for both the React frontend and Electron main/preload processes.
    7.  **README.md Update:**
        *   Document the confirmed tech stack.
        *   Add clear instructions for setup, running in development, and building the project.
        *   `theinternetuser` can lead this based on previous work.
    8.  **GitHub Project Board Update:**
        *   `theinternetuser` ensures tasks from this phase are on the GitHub Project board.

---

**Phase 2: Core Torrent Functionality & Backend Services**

*   **Objective:** Implement the non-UI logic for torrent handling and data persistence. Create service layers to abstract this logic.
*   **Tasks:**
    1.  **Torrent Engine - Final Decision & Integration:**
        *   **Decision:** `Cosmic Predator[YURI]` and `beater01` to finalize research: WebTorrent vs. a libtorrent Node.js wrapper. Consider ease of use in Node.js/Electron, streaming capabilities, file selection, resource usage.
        *   **Integration:** Install the chosen library.
        *   Create a `src/services/torrentService.ts` (or similar).
        *   Implement initial functions:
            *   `addTorrent(magnetOrFilePath: string): Promise<TorrentInfo>`
            *   `getTorrentDetails(torrentId: string): Promise<TorrentDetails>`
            *   (Basic stubs for pause, resume, remove)
        *   Initially, these can just log to console or interact minimally with the engine.
    2.  **Local Storage - SQLite Setup:**
        *   **Decision:** Choose an SQLite library compatible with Electron/Node.js (e.g., `better-sqlite3` is often recommended for Electron main process; `sqlite3` is another option).
        *   **Integration:** Install the library.
        *   Create a `src/services/storageService.ts` (or similar, accessible from the Electron main process, possibly exposed via preload).
        *   Define basic schema for:
            *   Torrents (id, name, magnetLink, addedDate, status, downloadPath, etc.)
            *   User Settings (e.g., defaultDownloadDir, theme)
        *   Implement functions:
            *   `initializeDatabase()`
            *   `saveTorrentInfo(torrent: TorrentInfo)`
            *   `getAllTorrents(): Promise<TorrentInfo[]>`
            *   `getUserSettings(): Promise<UserSettings>`
            *   `saveUserSettings(settings: UserSettings)`
    3.  **IPC (Inter-Process Communication) Setup:**
        *   Define clear channels and data structures for communication between the React renderer process (`src`) and the Electron main process (where `torrentService` and `storageService` might primarily live or be managed).
        *   Update `electron/preload.ts` to securely expose necessary functions/APIs to the renderer.
        *   Example: `window.electronAPI.addTorrent(magnetLink)`

---

**Phase 3: MVP UI & Feature Implementation**

*   **Objective:** Build the user interface for all MVP features, connecting them to the backend services created in Phase 2.
*   **Design Input:** `leme[WUWA]` to provide UI mockups/outlines using Stitch AI or other tools. These will guide UI development.
*   **Tasks (develop components in `src/components/` and views/screens in `src/pages/` or `src/views/`):**
    1.  **Global Layout & Navigation:**
        *   Create a main application layout (e.g., sidebar, main content area).
        *   Basic navigation (if needed for MVP, e.g., Torrents, Settings).
    2.  **"Add Torrent" UI:**
        *   Component/Modal for inputting magnet links or selecting .torrent files.
        *   Call `torrentService.addTorrent` via IPC.
        *   Provide user feedback (success/error).
    3.  **"Torrent List" View:**
        *   Display a list of torrents fetched from `storageService` (via Zustand store updated by `torrentService` events).
        *   For each torrent, show: Name, Progress (visual bar), Speed (Download/Upload), Status (Downloading, Paused, Seeding, Error), ETA.
        *   Use Headless UI components and Tailwind for styling.
    4.  **"Torrent File Selection" UI:**
        *   When a torrent is selected/clicked, show a view/modal listing its files.
        *   Allow users to select/deselect individual files for download.
        *   (This will influence how `torrentService` handles partial downloads).
    5.  **Basic Download Management Controls:**
        *   Buttons (on torrent list items or a details view) for: Pause, Resume, Remove Torrent (from list and optionally delete files).
        *   Connect these to `torrentService` functions via IPC.
    6.  **"Streaming Player" UI:**
        *   A dedicated view or modal for playing video/audio content.
        *   Integrate with the streaming capabilities of the chosen torrent engine. This might involve:
            *   Getting a stream URL or piping data from `torrentService`.
            *   Using an HTML5 `<video>` or `<audio>` element, potentially with a React player library (if simple controls aren't enough).
        *   Basic controls: Play/Pause, Volume, Seek, Fullscreen.
    7.  **Basic "Settings" UI:**
        *   Simple page to view/edit settings stored via `storageService`.
        *   Example: Default download location (if feasible for MVP), theme toggle.
    8.  **Zustand Store Integration:**
        *   Develop stores (`torrentStore.ts`, `settingsStore.ts`, etc.) to manage application state fetched from services and reflect UI changes.

---

**Phase 4: Refinement, Error Handling, and Basic Testing**

*   **Objective:** Improve the stability and user experience of the MVP.
*   **Tasks:**
    1.  **Comprehensive Error Handling:**
        *   Implement user-friendly error messages for: invalid torrents, download errors, storage issues, IPC failures.
        *   Graceful degradation where possible.
    2.  **Loading States & Feedback:**
        *   Show loading indicators during async operations (adding torrents, fetching files, etc.).
        *   Clear visual feedback for user actions.
    3.  **UX Polish:**
        *   Review UI flow based on `leme[WUWA]`'s designs and team feedback.
        *   Ensure consistent styling and intuitive interactions.
        *   Keyboard navigation and accessibility basics (Headless UI helps).
    4.  **Manual Testing by Team:**
        *   All team members test all MVP features thoroughly on their systems.
        *   Log bugs as GitHub Issues.
    5.  **Code Reviews:**
        *   Implement a Pull Request (PR) workflow.
        *   Team members review each other's code for quality, correctness, and adherence to standards.

---

**Phase 5: Packaging & Pre-Release**

*   **Objective:** Prepare the application for distribution.
*   **Tasks:**
    1.  **Configure `electron-builder.json5` (or chosen packager config):**
        *   Set up app ID, product name, version, author.
        *   Configure build targets (e.g., Windows installer, macOS .dmg, Linux .AppImage/.deb).
        *   Handle code signing (if applicable, can be complex).
    2.  **Application Icons:**
        *   Design/obtain icons for different platforms and sizes.
        *   Configure Electron Builder to use them.
    3.  **Build Release Candidates:**
        *   Generate packaged versions of the application.
    4.  **Internal Alpha/Beta Testing:**
        *   Team installs and tests the packaged application (not just dev mode).

---

**Phase 6: Post-MVP & Future Enhancements (Beyond this Plan)**

*   Gather user feedback (if released publicly or to a wider group).
*   Prioritize features from the "Future Features" list (user accounts, search, subtitles, etc.).
*   Address bugs found post-release.
*   Revisit mobile app development strategy if desired.
