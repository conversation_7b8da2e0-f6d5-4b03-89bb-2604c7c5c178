import { ipcRenderer, contextBridge } from 'electron';

const electronAPI = {
  addTorrent: (torrentId: string | Buffer) => ipcRenderer.invoke('add-torrent', torrentId),
  openFileDialog: () => ipcRenderer.invoke('open-file-dialog'),
  onDownloadProgress: (callback: (event: any, progress: any) => void) => {
    ipcRenderer.on('download-progress', callback);
    return () => {
      ipcRenderer.removeListener('download-progress', callback);
    };
  },
};

contextBridge.exposeInMainWorld('electronAPI', electronAPI);

export type ElectronAPI = typeof electronAPI;
